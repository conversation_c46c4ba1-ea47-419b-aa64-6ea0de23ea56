# LitLSTMSTAModel 使用指南

## 概述

`LitLSTMSTAModel` 是基于 PyTorch Lightning 框架的 LSTM-STA（空间-时间注意力）模型封装。它提供了完整的训练、验证和测试流程，支持注意力权重可视化和多种评估指标。

## 模型特性

### 核心功能
- **空间注意力机制**：捕获输入序列中不同时间步之间的空间关系
- **时间注意力机制**：自动学习重要时间步的权重
- **完整的Lightning集成**：支持自动训练、验证、测试流程
- **注意力权重可视化**：提供注意力权重分析功能
- **多维目标支持**：支持单步和多步预测任务

### 技术优势
- 自动混合精度训练支持
- 分布式训练支持
- 自动学习率调度
- 丰富的日志记录和监控
- 早停和模型检查点功能

## 快速开始

### 1. 基本使用

```python
import torch
import lightning as L
from src.model.lstm_sta import LitLSTMSTAModel

# 模型参数
input_size = 64          # 输入特征总维度
lstm_input_size = 8      # LSTM输入特征维度
lstm_hidden_size = 64    # LSTM隐藏层维度
output_size = 1          # 输出维度
sequence_length = 8      # 序列长度

# 参数约束检查
assert input_size == sequence_length * lstm_input_size

# 创建模型
model = LitLSTMSTAModel(
    input_size=input_size,
    lstm_input_size=lstm_input_size,
    lstm_hidden_size=lstm_hidden_size,
    output_size=output_size,
    sequence_length=sequence_length,
    dropout=0.1,
    learning_rate=1e-3,
    weight_decay=1e-5
)
```

### 2. 训练模型

```python
from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping
from lightning.pytorch.loggers import TensorBoardLogger

# 设置回调
checkpoint_callback = ModelCheckpoint(
    monitor="val_loss",
    filename="lstm-sta-{epoch:02d}-{val_loss:.4f}",
    save_top_k=3,
    mode="min",
)

early_stopping = EarlyStopping(
    monitor="val_loss",
    patience=10,
    mode="min",
)

# 设置训练器
trainer = L.Trainer(
    max_epochs=100,
    callbacks=[checkpoint_callback, early_stopping],
    logger=TensorBoardLogger("logs", name="lstm_sta"),
    accelerator="auto",
    devices=1,
    gradient_clip_val=1.0,
)

# 训练
trainer.fit(
    model=model,
    train_dataloaders=train_loader,
    val_dataloaders=val_loader,
)
```

### 3. 模型评估

```python
# 测试模型
test_results = trainer.test(model=model, dataloaders=test_loader)
print(f"测试结果: {test_results[0]}")

# 预测
predictions = trainer.predict(model=model, dataloaders=predict_loader)
```

### 4. 注意力权重可视化

```python
# 获取注意力权重
model.eval()
with torch.no_grad():
    spatial_weights, temporal_weights = model.get_attention_weights(input_data)

# spatial_weights: [batch_size, sequence_length, sequence_length]
# temporal_weights: [batch_size, sequence_length]

# 可视化代码示例
import matplotlib.pyplot as plt

# 空间注意力热图
plt.figure(figsize=(10, 8))
plt.imshow(spatial_weights[0].cpu().numpy(), cmap='Blues')
plt.title('空间注意力权重')
plt.xlabel('时间步')
plt.ylabel('时间步')
plt.colorbar()
plt.show()

# 时间注意力权重
plt.figure(figsize=(10, 4))
plt.bar(range(len(temporal_weights[0])), temporal_weights[0].cpu().numpy())
plt.title('时间注意力权重')
plt.xlabel('时间步')
plt.ylabel('注意力权重')
plt.show()
```

## 参数说明

### 模型参数
- `input_size`: 输入特征的总维度，必须等于 `sequence_length * lstm_input_size`
- `lstm_input_size`: LSTM输入特征的维度
- `lstm_hidden_size`: LSTM隐藏层的维度
- `output_size`: 输出特征的维度
- `sequence_length`: 序列长度，用于将输入重塑为时间序列
- `dropout`: Dropout比率，防止过拟合（默认：0.1）

### 训练参数
- `learning_rate`: 学习率（默认：1e-3）
- `weight_decay`: 权重衰减系数（默认：1e-5）

## 输入输出格式

### 输入格式
- **训练/验证/测试**: `(x, y)` 其中 `x` 形状为 `[batch_size, input_size]`，`y` 形状为 `[batch_size, output_size]` 或 `[batch_size, horizon, output_size]`
- **预测**: `x` 形状为 `[batch_size, input_size]`

### 输出格式
- **模型输出**: `[batch_size, output_size]`
- **注意力权重**: 
  - 空间注意力: `[batch_size, sequence_length, sequence_length]`
  - 时间注意力: `[batch_size, sequence_length]`

## 评估指标

模型自动计算并记录以下指标：
- **训练损失** (train_loss): MSE损失
- **验证损失** (val_loss): MSE损失
- **验证MAE** (val_mae): 平均绝对误差
- **验证RMSE** (val_rmse): 均方根误差
- **测试损失** (test_loss): MSE损失
- **测试MAE** (test_mae): 平均绝对误差
- **测试RMSE** (test_rmse): 均方根误差

## 最佳实践

### 1. 数据预处理
- 确保输入数据已正确归一化
- 验证 `input_size = sequence_length * lstm_input_size` 约束
- 使用适当的批次大小

### 2. 超参数调优
- 从较小的 `lstm_hidden_size` 开始（如32或64）
- 调整 `dropout` 比率以防止过拟合
- 使用学习率调度器自动调整学习率

### 3. 训练监控
- 使用 TensorBoard 监控训练过程
- 设置早停以防止过拟合
- 保存最佳模型检查点

### 4. 注意力分析
- 定期检查注意力权重分布
- 分析模型关注的时间步
- 验证注意力模式的合理性

## 完整示例

参考 `examples/lstm_sta_usage_example.py` 获取完整的使用示例，包括：
- 数据创建和预处理
- 模型训练和评估
- 注意力权重可视化
- TensorBoard集成

## 故障排除

### 常见问题
1. **参数约束错误**: 确保 `input_size == sequence_length * lstm_input_size`
2. **内存不足**: 减少批次大小或模型隐藏层维度
3. **训练不收敛**: 调整学习率或增加模型容量
4. **过拟合**: 增加dropout比率或使用更多正则化

### 性能优化
- 使用GPU加速训练
- 启用自动混合精度训练
- 使用适当的数据加载器设置
- 考虑分布式训练（多GPU）
