import torch
import torch.nn as nn
import torch.nn.functional as F


class CombinedLoss(nn.Module):
    """组合损失函数，结合多种损失以提高鲁棒性"""

    def __init__(
        self,
        mse_weight: float = 0.5,
        huber_weight: float = 0.3,
        mae_weight: float = 0.2,
        huber_delta: float = 1.0,
    ):
        super(CombinedLoss, self).__init__()
        self.mse_weight = mse_weight
        self.huber_weight = huber_weight
        self.mae_weight = mae_weight

        self.mse_loss = nn.MSELoss()
        self.huber_loss = nn.HuberLoss(delta=huber_delta)
        self.mae_loss = nn.L1Loss()

    def forward(self, pred, target):
        mse = self.mse_loss(pred, target)
        huber = self.huber_loss(pred, target)
        mae = self.mae_loss(pred, target)

        total_loss = (
            self.mse_weight * mse + self.huber_weight * huber + self.mae_weight * mae
        )

        return total_loss


class FocalLoss(nn.Module):
    """Focal Loss for regression tasks"""

    def __init__(self, alpha: float = 1.0, gamma: float = 2.0):
        super(Focal<PERSON>oss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma

    def forward(self, pred, target):
        mse = F.mse_loss(pred, target, reduction="none")
        pt = torch.exp(-mse)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * mse
        return focal_loss.mean()


class QuantileLoss(nn.Module):
    """分位数损失，用于不确定性估计"""

    def __init__(self, quantiles: list = [0.1, 0.5, 0.9]):
        super(QuantileLoss, self).__init__()
        self.quantiles = quantiles

    def forward(self, pred, target):
        """
        pred: [batch_size, seq_len, num_quantiles * num_features]
        target: [batch_size, seq_len, num_features]
        """
        batch_size, seq_len, _ = target.shape
        num_features = target.shape[-1]
        num_quantiles = len(self.quantiles)

        # 重塑预测值
        pred = pred.view(batch_size, seq_len, num_quantiles, num_features)
        target = target.unsqueeze(2).expand(-1, -1, num_quantiles, -1)

        losses = []
        for i, q in enumerate(self.quantiles):
            error = target - pred[:, :, i, :]
            loss = torch.max(q * error, (q - 1) * error)
            losses.append(loss)

        return torch.stack(losses).mean()


class AdaptiveLoss(nn.Module):
    """自适应损失函数，根据训练进度调整损失权重"""

    def __init__(self, base_loss_fn, adaptation_rate: float = 0.1):
        super(AdaptiveLoss, self).__init__()
        self.base_loss_fn = base_loss_fn
        self.adaptation_rate = adaptation_rate
        self.register_buffer("loss_history", torch.zeros(100))
        self.step_count = 0

    @property
    def loss_history(self) -> torch.Tensor:
        """获取损失历史tensor"""
        buffer = self._buffers.get("loss_history")
        if buffer is None:
            raise RuntimeError("loss_history buffer not found")
        return buffer

    def forward(self, pred, target):
        base_loss = self.base_loss_fn(pred, target)

        # 更新损失历史
        if self.step_count < 100:
            self.loss_history[self.step_count] = base_loss.detach()
        else:
            self.loss_history[:-1] = self.loss_history[1:]
            self.loss_history[-1] = base_loss.detach()

        self.step_count += 1

        # 计算自适应权重
        if self.step_count > 10:
            recent_avg = self.loss_history[-10:].mean()
            overall_avg = self.loss_history[: min(self.step_count, 100)].mean()

            if recent_avg > overall_avg:
                # 如果最近损失较高，增加权重
                weight = 1.0 + self.adaptation_rate
            else:
                # 如果最近损失较低，减少权重
                weight = 1.0 - self.adaptation_rate * 0.5

            return base_loss * weight

        return base_loss


class TemporalConsistencyLoss(nn.Module):
    """时间一致性损失，确保预测序列的平滑性"""

    def __init__(self, consistency_weight: float = 0.1):
        super(TemporalConsistencyLoss, self).__init__()
        self.consistency_weight = consistency_weight

    def forward(self, pred, target=None):
        """
        pred: [batch_size, seq_len, num_features]
        target: 未使用，保持接口一致性
        """
        # 计算相邻时间步的差异
        diff = pred[:, 1:, :] - pred[:, :-1, :]
        consistency_loss = torch.mean(diff**2)

        return self.consistency_weight * consistency_loss


def get_loss_function(loss_type: str, **kwargs):
    """获取损失函数的工厂函数"""

    if loss_type == "mse":
        return nn.MSELoss()
    elif loss_type == "huber":
        return nn.HuberLoss(delta=kwargs.get("delta", 1.0))
    elif loss_type == "smooth_l1":
        return nn.SmoothL1Loss()
    elif loss_type == "mae":
        return nn.L1Loss()
    elif loss_type == "combined":
        return CombinedLoss(
            mse_weight=kwargs.get("mse_weight", 0.5),
            huber_weight=kwargs.get("huber_weight", 0.3),
            mae_weight=kwargs.get("mae_weight", 0.2),
        )
    elif loss_type == "focal":
        return FocalLoss(alpha=kwargs.get("alpha", 1.0), gamma=kwargs.get("gamma", 2.0))
    elif loss_type == "quantile":
        return QuantileLoss(quantiles=kwargs.get("quantiles", [0.1, 0.5, 0.9]))
    elif loss_type == "adaptive":
        base_loss_type = kwargs.get("base_loss", "mse")
        if base_loss_type == "mse":
            base_loss = nn.MSELoss()
        elif base_loss_type == "huber":
            base_loss = nn.HuberLoss(delta=kwargs.get("delta", 1.0))
        else:
            base_loss = nn.MSELoss()  # 默认使用MSE
        return AdaptiveLoss(
            base_loss_fn=base_loss, adaptation_rate=kwargs.get("adaptation_rate", 0.1)
        )
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


class LossWithRegularization(nn.Module):
    """带正则化的损失函数"""

    def __init__(
        self,
        base_loss_fn,
        model,
        l1_weight: float = 0.0,
        l2_weight: float = 0.0,
        temporal_consistency_weight: float = 0.0,
    ):
        super(LossWithRegularization, self).__init__()
        self.base_loss_fn = base_loss_fn
        self.model = model
        self.l1_weight = l1_weight
        self.l2_weight = l2_weight
        self.temporal_consistency = TemporalConsistencyLoss(temporal_consistency_weight)

    def forward(self, pred, target):
        # 基础损失
        base_loss = self.base_loss_fn(pred, target)

        # L1正则化
        l1_reg = 0.0
        if self.l1_weight > 0:
            for param in self.model.parameters():
                l1_reg += torch.norm(param, 1)
            l1_reg *= self.l1_weight

        # L2正则化
        l2_reg = 0.0
        if self.l2_weight > 0:
            for param in self.model.parameters():
                l2_reg += torch.norm(param, 2)
            l2_reg *= self.l2_weight

        # 时间一致性损失
        temporal_loss = self.temporal_consistency(pred)

        total_loss = base_loss + l1_reg + l2_reg + temporal_loss

        return total_loss
