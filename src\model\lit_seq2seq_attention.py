import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning as L

# 调度器现在通过schedulers模块导入
from typing import Dict, List, Tuple, Optional, Union, Any

from .seq2seq_attention import Seq2SeqWithAttention, Encoder, DecoderWithAttention
from .losses import get_loss_function, LossWithRegularization
from .schedulers import get_scheduler


class LitSeq2SeqWithAttention(L.LightningModule):
    """
    PyTorch Lightning Seq2Seq with Attention模型

    这是对Seq2SeqWithAttention模型的Lightning封装，提供了完整的训练、验证和测试流程。
    适用于序列到序列的预测任务，包含注意力机制以提高模型性能。
    """

    def __init__(
        self,
        num_past_features: int,
        num_future_features: int,
        num_target_features: int,
        hidden_dim: int = 128,
        num_layers: int = 2,
        dropout_rate: float = 0.1,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
        loss_type: str = "mse",
        scheduler_type: str = "plateau",
        attention_type: str = "additive",
        use_teacher_forcing: bool = True,
        teacher_forcing_ratio: float = 0.5,
    ):
        """
        初始化Lightning Seq2Seq with Attention模型

        Args:
            num_past_features: 过去输入的特征数量
            num_future_features: 未来输入的特征数量
            num_target_features: 输出目标变量的数量
            hidden_dim: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            dropout_rate: dropout比率，默认为0.1
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
            loss_type: 损失函数类型，默认为"mse"
            scheduler_type: 学习率调度器类型，默认为"plateau"
        """
        super().__init__()
        self.save_hyperparameters()

        # 创建编码器和解码器
        encoder = Encoder(
            num_past_features=num_past_features,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout_rate=dropout_rate,
        )

        decoder = DecoderWithAttention(
            num_future_features=num_future_features,
            num_target_features=num_target_features,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout_rate=dropout_rate,
            attention_type=attention_type,
            use_teacher_forcing=use_teacher_forcing,
        )

        # 创建Seq2SeqWithAttention模型
        self.model = Seq2SeqWithAttention(
            encoder=encoder, decoder=decoder, use_teacher_forcing=use_teacher_forcing
        )

        # 保存teacher forcing参数
        self.teacher_forcing_ratio = teacher_forcing_ratio

        # 定义改进的损失函数
        if loss_type == "combined":
            self.criterion = get_loss_function("combined")
        elif loss_type == "huber":
            self.criterion = get_loss_function("huber", delta=1.0)
        elif loss_type == "focal":
            self.criterion = get_loss_function("focal", alpha=1.0, gamma=2.0)
        elif loss_type == "adaptive":
            self.criterion = get_loss_function("adaptive", base_loss="huber")
        else:
            self.criterion = get_loss_function(loss_type)

        # 保存调度器类型
        self.scheduler_type = scheduler_type

        # 记录训练参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

    def forward(self, x_past: torch.Tensor, x_future: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x_past: 过去的数据，形状为 [batch_size, past_seq_len, num_past_features]
            x_future: 未来的已知数据，形状为 [batch_size, future_seq_len, num_future_features]

        Returns:
            输出张量，形状为 [batch_size, future_seq_len, num_target_features]
        """
        return self.model(x_past, x_future)

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤，支持teacher forcing

        Args:
            batch: 包含 (x_past, x_future, y) 的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x_past, x_future, y = batch

        # 前向传播，使用teacher forcing
        y_hat = self.model(x_past, x_future, y, self.teacher_forcing_ratio)

        # 计算损失
        loss = self.criterion(y_hat, y)

        # 记录损失
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含 (x_past, x_future, y) 的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x_past, x_future, y = batch
        y_hat = self(x_past, x_future)

        # 计算损失和指标
        val_loss = self.criterion(y_hat, y)
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(val_loss)

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True, on_epoch=True)
        self.log("val_mae", mae, on_epoch=True)
        self.log("val_rmse", rmse, on_epoch=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含 (x_past, x_future, y) 的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x_past, x_future, y = batch
        y_hat = self(x_past, x_future)

        # 计算损失和指标
        test_loss = self.criterion(y_hat, y)
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(test_loss)

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}

    def predict_step(
        self,
        batch: Union[Tuple[torch.Tensor, torch.Tensor], torch.Tensor],
        batch_idx: int,
    ) -> torch.Tensor:
        """
        预测步骤

        Args:
            batch: 输入张量或包含 (x_past, x_future) 的元组
            batch_idx: 批次索引

        Returns:
            预测结果
        """
        if isinstance(batch, tuple):
            x_past, x_future = batch
        else:
            # 如果只有一个输入，假设需要分割
            raise ValueError("预测步骤需要 (x_past, x_future) 两个输入")

        return self(x_past, x_future)

    def get_attention_weights(
        self, x_past: torch.Tensor, x_future: torch.Tensor
    ) -> List[torch.Tensor]:
        """
        获取注意力权重，用于可视化分析

        Args:
            x_past: 过去的数据，形状为 [batch_size, past_seq_len, num_past_features]
            x_future: 未来的已知数据，形状为 [batch_size, future_seq_len, num_future_features]

        Returns:
            每个时间步的注意力权重列表
        """
        self.eval()
        with torch.no_grad():
            batch_size = x_past.size(0)
            future_seq_len = x_future.size(1)
            attention_weights_list = []

            # 运行编码器
            encoder_outputs, hidden, cell = self.model.encoder(x_past)

            # 逐步运行解码器并收集注意力权重
            for t in range(future_seq_len):
                x_future_step = x_future[:, t, :].unsqueeze(1)

                # 计算注意力权重
                attention_weights = self.model.decoder.attention(
                    hidden, encoder_outputs
                )
                attention_weights_list.append(attention_weights)

                # 进行一步解码（更新hidden和cell状态）
                _, hidden, cell = self.model.decoder(
                    x_future_step, hidden, cell, encoder_outputs
                )

            return attention_weights_list

    def configure_optimizers(self) -> Dict[str, Any]:
        """
        配置优化器和学习率调度器

        Returns:
            包含优化器和学习率调度器的字典
        """
        # 使用AdamW优化器，通常比Adam更好
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.get("learning_rate", 1e-3),
            weight_decay=self.hparams.get("weight_decay", 1e-5),
        )

        # 根据配置选择调度器
        scheduler_type = self.hparams.get("scheduler_type", "plateau")

        if scheduler_type == "cosine":
            scheduler = {
                "scheduler": get_scheduler("cosine", optimizer, T_max=50, eta_min=1e-8),
                "interval": "epoch",
                "frequency": 1,
            }
        elif scheduler_type == "cosine_warmup":
            scheduler = {
                "scheduler": get_scheduler(
                    "cosine_warmup",
                    optimizer,
                    first_cycle_steps=50,
                    max_lr=self.hparams.get("learning_rate", 1e-3),
                    min_lr=1e-8,
                    warmup_steps=self.hparams.get("warmup_epochs", 5),
                ),
                "interval": "epoch",
                "frequency": 1,
            }
        elif scheduler_type == "onecycle":
            scheduler = {
                "scheduler": get_scheduler(
                    "onecycle",
                    optimizer,
                    max_lr=self.hparams.get("learning_rate", 1e-3),
                    total_steps=1000,  # 估计值，实际使用时需要计算
                    pct_start=0.3,
                ),
                "interval": "step",
                "frequency": 1,
            }
        else:  # plateau (默认)
            scheduler = {
                "scheduler": get_scheduler(
                    "plateau",
                    optimizer,
                    mode="min",
                    factor=0.5,
                    patience=10,
                    min_lr=1e-8,
                ),
                "monitor": "val_loss",
                "interval": "epoch",
                "frequency": 1,
            }

        return {"optimizer": optimizer, "lr_scheduler": scheduler}
