# LSTM模型使用示例

本文档提供了如何使用项目中实现的LSTM模型进行时间序列预测的示例。

## 1. 单步预测示例

以下示例展示了如何使用训练好的单步预测LSTM模型进行预测：

```python
import torch
import lightning as L
from src.model.lstm import LitLSTM
import pandas as pd
import numpy as np
from datetime import datetime

# 加载训练好的模型
model_path = "lightning_logs/version_0/checkpoints/lstm-epoch=42-val_loss=0.0123.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)
model.eval()  # 设置为评估模式

# 示例：准备输入数据
# 假设我们有一个时间序列数据，包含28个特征
sequence_length = 255  # 与训练时使用的lookback相同
feature_count = 28    # 特征数量

# 创建示例数据，实际应用中应使用真实数据
sample_data = np.random.randn(sequence_length, feature_count)
input_tensor = torch.tensor(sample_data, dtype=torch.float32).unsqueeze(0)  # 添加批次维度 [1, seq_len, features]

# 进行预测
with torch.no_grad():
    prediction = model(input_tensor)

print(f"预测结果形状: {prediction.shape}")
print(f"预测值: {prediction.numpy()}")
```

## 2. 多步预测示例

以下示例展示了如何使用训练好的多步预测LSTM模型：

```python
import torch
from src.model.lstm import LitLSTMMultiStep
import numpy as np

# 加载训练好的多步预测模型
model_path = "lightning_logs/version_1/checkpoints/lstm-epoch=38-val_loss=0.0156.ckpt"
model = LitLSTMMultiStep.load_from_checkpoint(model_path)
model.eval()  # 设置为评估模式

# 准备输入数据
sequence_length = 255  # 与训练时使用的lookback相同
feature_count = 28    # 特征数量
horizon = 5          # 预测的时间步数

# 创建示例数据
sample_data = np.random.randn(sequence_length, feature_count)
input_tensor = torch.tensor(sample_data, dtype=torch.float32).unsqueeze(0)  # [1, seq_len, features]

# 进行预测
with torch.no_grad():
    prediction = model(input_tensor)

print(f"预测结果形状: {prediction.shape}")  # 应为 [1, horizon, output_dim]
print(f"未来{horizon}步的预测值:")
for step in range(horizon):
    print(f"  时间步 {step+1}: {prediction[0, step].numpy()}")
```

## 3. 使用模型进行批量预测

以下示例展示了如何使用模型进行批量预测：

```python
import torch
from src.model.lstm import LitLSTM
import numpy as np

# 加载模型
model_path = "lightning_logs/version_0/checkpoints/lstm-epoch=42-val_loss=0.0123.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)
model.eval()

# 准备批量数据
batch_size = 32
sequence_length = 255
feature_count = 28

# 创建随机批量数据
batch_data = np.random.randn(batch_size, sequence_length, feature_count)
batch_tensor = torch.tensor(batch_data, dtype=torch.float32)

# 批量预测
with torch.no_grad():
    batch_predictions = model(batch_tensor)

print(f"批量预测结果形状: {batch_predictions.shape}")  # 应为 [batch_size, output_size]
```

## 4. 从真实数据加载并预测

以下示例展示了如何从CSV文件加载数据，预处理后进行预测：

```python
import torch
import pandas as pd
import numpy as np
from src.model.lstm import LitLSTM
from src.utils.data_utils import normalize_df_by_params

# 加载模型
model_path = "lightning_logs/version_0/checkpoints/lstm-epoch=42-val_loss=0.0123.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)
model.eval()

# 加载数据
data_path = "data/source/FLFTR10.csv"
data = pd.read_csv(data_path)

# 预处理数据（这里简化了预处理步骤，实际应用应与训练时相同）
# 假设我们已经有了归一化参数
normalize_params = {
    'method': 'min-max',
    'min_values': {...},  # 从模型保存的元数据中加载
    'max_values': {...}   # 从模型保存的元数据中加载
}

# 归一化数据
processed_data = normalize_df_by_params(data, normalize_params)

# 选择特征列
feature_columns = [...]  # 与训练时相同的特征列
lookback = 255

# 准备输入序列
input_sequence = processed_data[feature_columns].values[-lookback:]
input_tensor = torch.tensor(input_sequence, dtype=torch.float32).unsqueeze(0)  # [1, seq_len, features]

# 进行预测
with torch.no_grad():
    prediction = model(input_tensor)

# 反归一化预测结果
# 这里需要应用与训练时相同的反归一化处理

print(f"预测结果: {prediction.numpy()}")
```

## 5. 使用Lightning Trainer进行预测

以下示例展示了如何使用Lightning的Trainer接口进行预测：

```python
import torch
import lightning as L
from src.model.lstm import LitLSTM
import numpy as np

# 加载模型
model_path = "lightning_logs/version_0/checkpoints/lstm-epoch=42-val_loss=0.0123.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)

# 创建一个简单的预测数据集
class PredictionDataset(torch.utils.data.Dataset):
    def __init__(self, num_samples, seq_length, feature_count):
        self.data = np.random.randn(num_samples, seq_length, feature_count)
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return torch.tensor(self.data[idx], dtype=torch.float32)

# 创建数据集和数据加载器
dataset = PredictionDataset(num_samples=10, seq_length=255, feature_count=28)
dataloader = torch.utils.data.DataLoader(dataset, batch_size=4)

# 创建训练器
trainer = L.Trainer(accelerator="cpu")

# 进行预测
predictions = trainer.predict(model, dataloader)

# 处理预测结果
for i, pred_batch in enumerate(predictions):
    print(f"Batch {i} predictions shape: {pred_batch.shape}")
```

## 6. 模型性能评估

以下示例展示了如何评估模型在测试集上的性能：

```python
import torch
import lightning as L
from src.model.lstm import LitLSTM
from src.data_loader import FlftrLoader
from datetime import datetime

# 初始化数据加载器
start_time = datetime.strptime("2023-01-01", "%Y-%m-%d")
end_time = datetime.strptime("2023-12-31", "%Y-%m-%d")
work_dir = "data/test"

data_config = {
    "debug": True,
    "freq": "1min",
    "min_data_length": 512,
    "split_ratio": [0.8, 0.1, 0.1],
    "normalize_method": "min-max",
    "seq_length": 256,
    "lookback": 255,
    "horizon": 1,
    "batch_size": 32
}

data_module = FlftrLoader(
    start_time=start_time,
    end_time=end_time,
    work_dir=work_dir,
    config=data_config
)

# 准备数据
data_module.prepare_data()
data_module.setup(stage="test")

# 加载模型
model_path = "lightning_logs/version_0/checkpoints/lstm-epoch=42-val_loss=0.0123.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)

# 创建训练器
trainer = L.Trainer(accelerator="cpu")

# 评估模型
test_results = trainer.test(model, datamodule=data_module)[0]

print("测试结果:")
for metric, value in test_results.items():
    print(f"  {metric}: {value:.4f}")
``` 